#include <stdio.h>
#include <stdlib.h>
#include <string.h>


typedef enum {
    CHT, 
    FET, 
    TWN  
} Operator;


typedef union {
    struct {
        int area_code;
        int number;
    } home;
    struct {
        Operator operator;
        int number;
    } cellular;
} PhoneNumber;


typedef struct Node {
    char name[10];            
    char phone_type[10];      
    PhoneNumber phone;        
    struct Node *next;        
} Node;

/* 函數宣告 */
Node *create_node(const char *name, const char *phone_type, PhoneNumber phone);
void append_node(Node **head, Node *new_node);
void print_list(const Node *head);

int main() {
    Node *head = NULL; 
    char name[10];
    char phone_type[10];
    PhoneNumber phone;
    int choice;

    while (1) {
        printf("Enter user name (max 10 characters, or 'exit' to stop): ");
        scanf("%s", name);
        if (strcmp(name, "exit") == 0) {
            break;
        }

        printf("Enter phone type (home/cellular): ");
        scanf("%s", phone_type);

        if (strcmp(phone_type, "home") == 0) {
            printf("Enter area code: ");
            scanf("%d", &phone.home.area_code);
            printf("Enter number: ");
            scanf("%d", &phone.home.number);
        } else if (strcmp(phone_type, "cellular") == 0) {
            printf("Select operator (0: CHT, 1: FET, 2: TWN): ");
            scanf("%d", &choice);
            phone.cellular.operator = (Operator)choice;
            printf("Enter number: ");
            scanf("%d", &phone.cellular.number);
        } else {
            printf("Invalid phone type. Try again.\n");
            continue;
        }

        Node *new_node = create_node(name, phone_type, phone);
        append_node(&head, new_node);
        print_list(head); 
    }


    Node *current = head;
    while (current != NULL) {
        Node *temp = current;
        current = current->next;
        free(temp);
    }

    return 0;
}


Node *create_node(const char *name, const char *phone_type, PhoneNumber phone) {
    Node *new_node = (Node *)malloc(sizeof(Node));
    if (!new_node) {
        printf("Memory allocation failed!\n");
        exit(1);
    }
    strncpy(new_node->name, name, 10);
    strncpy(new_node->phone_type, phone_type, 10);
    new_node->phone = phone;
    new_node->next = NULL;
    return new_node;
}


void append_node(Node **head, Node *new_node) {
    if (*head == NULL) {
        *head = new_node;
    } else {
        Node *current = *head;
        while (current->next != NULL) {
            current = current->next;
        }
        current->next = new_node;
    }
}


void print_list(const Node *head) {
    const Node *current = head;
    printf("\n--- User Information ---\n");
    while (current != NULL) {
        printf("Name: %s\n", current->name);
        printf("Phone Type: %s\n", current->phone_type);
        if (strcmp(current->phone_type, "home") == 0) {
            printf("  Home Phone: (0%d) %d\n",
                   current->phone.home.area_code,
                   current->phone.home.number);
        } else if (strcmp(current->phone_type, "cellular") == 0) {
            const char *operators[] = {"CHT", "FET", "TWN"};
            printf("  Cellular Phone: Operator: %s, Number: 0%d\n",
                   operators[current->phone.cellular.operator],
                   current->phone.cellular.number);
        }
        current = current->next;
    }
    printf("-------------------------\n");
}

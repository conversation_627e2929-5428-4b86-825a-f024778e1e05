#include <stdio.h>
#include <stdlib.h>

enum Type {type_home = 1, type_cellular};
enum Operator {CHT = 1, FET, TWN};

typedef struct home {
    char area_code[3];
    char number[9];
}Home;

typedef struct cellular {
    enum Operator operator;
    char number[11];
}Cellular;

typedef struct User {
    char name[10];
    enum Type type;
    union 
    {
        Home home;
        Cellular cellular;
    }phone;
}User;

typedef struct node_info {
    User *user;
    struct node_info *next;
    struct node_info *prev;
}tQueueNode; 

typedef struct tQueue {
    tQueueNode *front;
    tQueueNode *rear;
    int count;
}tQueue;

tQueue* createQueue(void);
int enqueue_node(tQueue *queue, User *user);
void print_queue (tQueue *queue);

int main (void)
{
    tQueue *queue = createQueue();

    while (1)
    {
        //char name[10];
        enum Type type;
        User *user = (User *)malloc(sizeof(User));
        printf("Please enter your name: ");
        scanf("%9s", user->name);
        while (getchar() != '\n');       // 如果超過9字元清除緩衝區

        printf("Home phone type 1, cellular phone type 2: ");
        scanf(" %d", &type);

        switch (type)
        {
        case type_home:
            user->type = type;
            printf("Please enter area code: ");
            scanf("%2s", user->phone.home.area_code);
            while (getchar() != '\n');

            printf("Please enter phone number: ");
            scanf("%8s", user->phone.home.number);
            while (getchar() != '\n');
            enqueue_node(queue, user);
            break;
        
        case type_cellular:
            user->type = type;
            enum Operator operator;
            printf("Please select network operator (1. CHT, 2. FET, 3. TWN): ");
            scanf("%d", &operator);
            if (operator < 1 || operator > 3)
            {
                printf("Wrong network operator!\n");
            }
            user->phone.cellular.operator = operator;

            printf("Please enter phone number: ");
            scanf("%10s", user->phone.cellular.number);
            while (getchar() != '\n');
            enqueue_node(queue, user);
            break;

        default:
            printf("Wrong type!\n");
            break;
        }
        print_queue(queue);
    }
}

tQueue* createQueue(void){    
    tQueue *queue;
    queue=(tQueue *) malloc (sizeof(tQueue));

    if (queue)
    {
        queue->front=NULL;
        queue->rear=NULL;  
        queue->count=0;
    }

    return queue;
}

int enqueue_node(tQueue *queue, User *user)
{
    tQueueNode *queue_node = (tQueueNode *)malloc(sizeof(tQueueNode));
    queue_node->user = user;
    queue_node->next = NULL;
    queue_node->prev = NULL;

    if (queue->count == 0)
    {
        queue->front = queue_node;
        queue->rear = queue_node;
    }
    else
    {
        queue->rear->next = queue_node;
        queue_node->prev = queue->rear;
        queue->rear = queue_node;
    }
    queue->count++;
    return 1;
}

void print_queue (tQueue *queue)
{
    int i;
    tQueueNode *target = queue->front;

    printf("      queue: ");    
    for (i = 0; i < queue->count; i++)
    {
        switch (target->user->type)
        {
        case type_home:
            printf ("(name: %s, type: %s, area code: %s, number: %s) -> ", target->user->name, "home", target->user->phone.home.area_code, target->user->phone.home.number);
            break;
        
        case type_cellular:
            printf ("(name: %s, type: %s, number: %s, ", target->user->name, "cellular", target->user->phone.cellular.number);
            switch (target->user->phone.cellular.operator)
            {
            case CHT:
                printf("operator: CHT) -> ");
                break;
            case FET:
                printf("operator: FET) -> ");
                break;
            case TWN:
                printf("operator: CHT) -> ");
                break;
            }
        }
        target = target->next;
    }
    printf("\n");
}
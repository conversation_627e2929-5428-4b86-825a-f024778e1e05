# 教學助理 Agent 配置文件

# AI 模型配置
ai_models:
  default: "gemini"
  gemini:
    model_name: "gemini-2.0-flash"
    api_key_env: "GEMINI_API_KEY"
    temperature: 0.3
    max_tokens: 4096
  openai:
    model_name: "gpt-4"
    api_key_env: "OPENAI_API_KEY"
    temperature: 0.3
    max_tokens: 4096

# 文件處理配置
file_processing:
  supported_archives: [".zip", ".rar", ".7z", ".tar", ".tar.gz", ".tar.bz2"]
  supported_code_files: [".c", ".cpp", ".h"]
  extract_path: "./data/assignments/extracted"
  
# 代碼執行配置
code_execution:
  compiler: "gcc"
  compile_flags: ["-Wall", "-Wextra", "-std=c99"]
  timeout_seconds: 30
  max_output_size: 10240  # 10KB

# 評分配置
grading:
  max_score: 100
  weights:
    correctness: 0.7    # 代碼正確性權重
    style: 0.3          # 代碼風格權重
  
# 輸出配置
output:
  grades_file: "./data/grades/grades.csv"
  detailed_reports: "./data/grades/detailed_reports"
  
# 日誌配置
logging:
  level: "INFO"
  file: "./logs/agent.log"

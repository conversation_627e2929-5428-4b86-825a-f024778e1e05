#include <stdio.h>
#include <stdlib.h>
#include <string.h>

typedef enum {
    CHT,
    FET,
    TWN
} Operator;

typedef union 
{   
    struct {
        int areacode;
        long long number;
    }home;
    struct 
    {
        Operator operator;
        long long number;
    }cellsuer;
}phonenumber;

typedef struct User
{
   phonenumber phone;
   char name[10];//名字最多10個字 
   struct User *next;
   char phonetype;
}User;


void free_list(User *head)
{
    User *temp;
    while(head!=NULL)
    {
        temp = head;
        head = head->next;
        free(temp);
    }
}
void adduser(User **head)
{
    User *new_user = (User *) malloc(sizeof(User)); 
    printf("Enter name (max 9 characters): ");
    scanf("%9s",new_user->name);
    getchar();
    printf("Enter phone number type ('h' for home , 'c' for celluser): ");
    scanf(" %c",&new_user->phonetype);
    if(new_user->phonetype == 'h')
    {
        printf("Enter area code (max 4 characters): ");
        scanf("%d",&new_user->phone.home.areacode);
        printf("Enter home phome number: ");
        scanf("%lld",&new_user->phone.home.number);

    }
    else if (new_user->phonetype == 'c')
    {
        printf("Select operator (0:CHT ,1:FET ,2:TWN): ");
        scanf("%d",&new_user->phone.cellsuer.operator);
        if (!(new_user->phone.cellsuer.operator >=0 && new_user->phone.cellsuer.operator <= 2))
        {
            printf("Invalid operation");
            return;
        }
        printf("Enter home phome number: ");
        scanf("%lld",&new_user->phone.cellsuer.number);
    }
    else
    {
        printf("invalid operation!!!");
        return;
    }
    new_user->next = *head;// 把 newUser 的 next 指向 list
    *head = new_user;
    printf("User add\n");

}
void printuser(User *head)
{
    User *temp = head;
    char operation;
    printf("\nUser information\n");
    printf("-------------------\n");
    while(temp != NULL)
    {
        printf("Name : %s\n",temp->name);
        if(temp->phonetype == 'c')
        {
            printf("Phone type : Celluser\n");
        }
        else
        {
            printf("Phone type : Home\n");
        }
        operation = temp->phone.cellsuer.operator;
        if(temp->phone.cellsuer.operator!='\0')
        {
            switch (operation)
            {
                case FET:printf("Operater : FET\n");  /* constant-expression */
                break;
                case CHT:printf("Operater : CHT\n");  /* constant-expression */
                break;
                case TWN:printf("Operater : TWN\n");  /* constant-expression */
                break;
            default:
                break;
            }
            printf("Number: %lld\n",temp->phone.cellsuer.number);
            printf("-------------------\n");
        }
        else
        {
            printf("Area Code : %d\n",temp->phone.home.areacode);
            printf("Number: %lld\n",temp->phone.home.number);
            printf("-------------------\n");
        }
        temp = temp->next;


    }
}



int main()
{   
    User *list = NULL;
    while(1)
    {
        char add_user_or_not;
        //這邊使用雙指標是因為我需要去更改 list 裡面的值也就是指標的值，所以我不能直接指傳遞指標而是要去傳遞指標的位址，那這時候我就需要指標的指標去接我所傳遞的指標的位址
        adduser(&list);
        printf("do you want to keep add use (y/n)? ");
        scanf(" %c",&add_user_or_not);
        if(add_user_or_not == 'y'|| add_user_or_not =='Y')
        {
            continue;
        }
        else
        {
            break;
        }

        
        
    }
    printuser(list);
    free_list(list);
    return 0;
}


#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define NAME_LEN 10

enum Operator {CHT, FET, TWN};

typedef struct Contact {
    char name[NAME_LEN];
    int phoneType; 
    union {
        struct {
            int area_code;
            long number;
        } home;

        struct {
            enum Operator operator;
            long number;
        } cellular;
    };
    struct Contact* next;
} Contact;

Contact* head = NULL;


void freeContacts() {
    Contact* current = head;
    while (current != NULL) {
        Contact* temp = current;
        current = current->next;
        free(temp);
    }
}


void addContact() {
    Contact* newContact = malloc(sizeof(Contact));
    if (!newContact) {
        printf("Memory allocation failed!\n");
        return;
    }
    memset(newContact, 0, sizeof(Contact));

    printf("  Enter name : ", NAME_LEN - 1);
    scanf("%s", newContact->name);

    printf("  Enter phone type (1 for home, 2 for cellular): ");
    scanf("%d", &newContact->phoneType);

    if (newContact->phoneType == 1) {
        printf("  Enter area code and number (ex. 02 xxxxxxxx): ");
        scanf("%d %ld", &newContact->home.area_code, &newContact->home.number);
    } else if (newContact->phoneType == 2) {
        int op;
        printf("  Enter operator (0 for CHT, 1 for FET, 2 for TWN): ");
        scanf("%d", &op);
        newContact->cellular.operator = (enum Operator)op;

        printf("  Enter number: ");
        scanf("%ld", &newContact->cellular.number);
    } else {
        printf("  Invalid phone type.\n");
        free(newContact);
        return;
    }

    newContact->next = head;
    head = newContact;
}


void printContact(Contact* contact) {
    printf("    Name: %s\n", contact->name);
    if (contact->phoneType == 1) {
        printf("    Home number: (%02d) %ld\n", contact->home.area_code, contact->home.number);
    } else if (contact->phoneType == 2) {
        printf("    Cellular number:");
        switch (contact->cellular.operator) {
            case CHT: printf(" (CHT)"); break;
            case FET: printf(" (FET)"); break;
            case TWN: printf(" (TWN)"); break;
            default: printf(" (Unknown)"); break;
        }
        printf(" %ld\n", contact->cellular.number);
    }
}


void printContacts() {
    Contact* current = head;

    printf("\n  List (name and phone number)\n");
    printf("  ---------------------------\n");
    int list_number = 1;
    while (current != NULL) {
        printf("  %d.\n", list_number);
        printContact(current);
        current = current->next;
        list_number++;
    }
    printf("\n");
}

int main() {

    atexit(freeContacts);

    printf("Name and phone recorder:\n");
    while (1) {
        addContact();
        printContacts();
    }

    return 0;
}

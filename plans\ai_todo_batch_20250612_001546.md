```markdown
# C語言作業自動批改系統 - 批量處理執行計劃 🚀

## 1. 任務概覽

*   **任務名稱:** C語言作業批量自動批改
*   **任務類型:** Batch
*   **創建時間:** 2025-06-12 00:15:35
*   **處理目錄:** `data\assignments\`
*   **預估文件數量:** 1 (可擴展)
*   **AI 模型:** Gemini
*   **目標:** 高效、準確地批量批改C語言作業，生成詳細報告。

## 2. 處理目標

*   批量解壓縮指定目錄下的所有壓縮包（ZIP、RAR、7Z等）。
*   智能識別每個作業中的C語言代碼文件，並確定主文件（main.c或其他包含main函數的文件）。
*   編譯每個作業的C語言代碼。
*   執行編譯後的程序，並根據預設測試用例進行測試。
*   使用 Gemini 模型對代碼的正確性和風格進行評分（正確性70%，風格30%）。
*   生成每個作業的詳細評分報告，包括代碼、編譯信息、執行結果、測試用例結果、AI評分和評語。
*   生成班級整體分析報告，包括平均分、最高分、最低分、各項指標分布等。
*   提供進度監控和統計分析，方便追蹤批改進度。

## 3. 詳細執行計劃

### 階段 1: 環境準備與配置 ⚙️

*   [ ] 確認系統已安裝必要的編譯器（GCC）和解壓縮工具（7-Zip, Unrar等）。
*   [ ] 檢查 Gemini 模型 API 密钥是否有效，并配置到系统中。
*   [ ] 創建臨時文件夾用於存放解壓縮後的作業文件。
*   [ ] 設置資源監控，監控CPU、內存使用情況。
*   [ ] 配置日誌記錄，記錄所有操作和錯誤信息。

### 階段 2: 文件解壓縮與預處理 📦

*   [ ] 掃描 `data\assignments\` 目錄下的所有壓縮包文件。
*   [ ] 針對每個壓縮包文件，自動判斷壓縮格式（ZIP、RAR、7Z等）。
*   [ ] 將壓縮包解壓縮到臨時文件夾中，每個作業一個子文件夾。
*   [ ] 錯誤處理：如果解壓縮失敗，記錄錯誤信息並跳過該文件。
*   [ ] 統計成功解壓縮的文件數量。

### 階段 3: 代碼識別與主文件檢測 🔍

*   [ ] 遍歷每個作業的子文件夾，查找C語言代碼文件（.c）。
*   [ ] 智能檢測主文件（包含`main`函數的文件），如果有多個，提示錯誤。
*   [ ] 如果找不到C語言代碼文件，記錄錯誤信息並跳過該作業。
*   [ ] 記錄每個作業的主文件名。

### 階段 4: 代碼編譯與執行 💻

*   [ ] 使用 GCC 編譯每個作業的主文件。
*   [ ] 錯誤處理：如果編譯失敗，記錄錯誤信息並跳過該作業。
*   [ ] 執行編譯後的程序，並將輸出重定向到文件。
*   [ ] 針對每個作業，執行預設的測試用例，並記錄執行結果。
*   [ ] 設置執行超時時間，防止無限循環。

### 階段 5: AI 智能評分 🤖

*   [ ] 將每個作業的代碼提交給 Gemini 模型進行評分。
*   [ ] Gemini 模型根據代碼的正確性和風格進行評分（正確性70%，風格30%）。
*   [ ] 記錄 Gemini 模型的評分結果。
*   [ ] 錯誤處理：如果 Gemini 模型 API 调用失败，记录错误信息并使用默认评分。

### 階段 6: 報告生成與成績管理 📝

*   [ ] 針對每個作業，生成詳細的評分報告，包括：
    *   代碼
    *   編譯信息
    *   執行結果
    *   測試用例結果
    *   AI 評分和評語
*   [ ] 將評分報告保存到指定目錄。
*   [ ] 更新成績管理系統，記錄每個學生的成績。
*   [ ] 生成班級整體分析報告，包括平均分、最高分、最低分、各項指標分布等。

### 階段 7: 清理與歸檔 🧹

*   [ ] 清理臨時文件夾中的所有文件。
*   [ ] 將所有評分報告和分析報告歸檔到指定目錄。
*   [ ] 記錄所有操作的日誌信息。

### 階段 8: 進度監控與統計分析 📊

*   [ ] 實時顯示批改進度，包括已處理文件數量、剩餘文件數量、成功率、失敗率等。
*   [ ] 提供統計分析功能，分析各項指標的分布情況。
*   [ ] 生成進度報告，方便追蹤批改進度。

## 4. 預期結果

*   所有壓縮包文件被成功解壓縮。
*   每個作業的C語言代碼被成功編譯和執行。
*   每個作業都獲得了基於代碼正確性和風格的AI評分。
*   生成了每個作業的詳細評分報告。
*   生成了班級整體分析報告。
*   提供了進度監控和統計分析。

## 5. 風險點和注意事項

*   **壓縮包格式不兼容:** 確保系統支持所有常見的壓縮包格式。如果遇到不支持的格式，需要添加相應的解壓縮工具。
*   **代碼編譯錯誤:** 不同的編譯環境可能導致編譯錯誤。需要提供詳細的編譯錯誤信息，方便學生修改。
*   **程序執行超時:** 設置執行超時時間，防止無限循環。
*   **AI 評分不準確:** Gemini 模型的評分可能存在偏差。需要定期檢查和調整評分標準。
*   **資源消耗過大:** 大量文件處理可能導致資源消耗過大。需要監控CPU、內存使用情況，並優化代碼。
*   **文件路徑問題:** 確保文件路徑的正確性，避免文件讀寫錯誤。
*   **並發問題:** 如果支持並發處理，需要考慮線程安全問題。
*   **API 调用限制:** Gemini 模型 API 可能存在调用次数限制，需要合理控制调用频率。

## 6. 成功標準

*   所有作業均已完成自動批改。
*   每個作業都生成了詳細的評分報告。
*   班級整體分析報告已生成。
*   系統運行穩定，沒有出現嚴重錯誤。
*   資源消耗在可接受範圍內。
*   批改效率滿足要求。
```
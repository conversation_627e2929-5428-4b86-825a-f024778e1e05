#include <stdio.h>
#include <stdlib.h>
#include <string.h>

typedef enum {
    CHT, // 中華電信
    FET, // 遠傳電信
    TWN  // 台灣大哥大
} Operator;

typedef union {
    struct {
        int area_code; // 家用電話的區碼
        int number;    // 家用電話的號碼
    } home;
    struct {
        Operator operator; // 行動電話的運營商
        int number;        // 行動電話的號碼
    } cellular;
} PhoneNumber;

// 定義鏈結串列節點結構
typedef struct Node {
    char name[10];       
    int type;            
    PhoneNumber phone;   
    struct Node *next;   
} Node;

Node *createNode();
void addNode(Node **head);
void printList(Node *head);

int main() {
    Node *head = NULL; 
    int choice;

    while (1) {
        printf("\n1. Add new user\n2. Print all users\n3. Exit\nEnter your choice: ");
        scanf("%d", &choice);

        switch (choice) {
            case 1:
                addNode(&head);
                break;
            case 2:
                printList(head);
                break;
            case 3:
                printf("Exiting program...\n");
                while (head) {
                    Node *temp = head;
                    head = head->next;
                    free(temp);
                }
                return 0;
            default:
                printf("Invalid choice. Please try again.\n");
        }
    }

    return 0;
}

Node *createNode() {
    Node *newNode = (Node *)malloc(sizeof(Node));
    if (!newNode) {
        printf("Memory allocation failed!\n");
        exit(1);
    }
    return newNode;
}

void addNode(Node **head) {
    Node *newNode = createNode();
    Node *current;

    printf("Enter name (max 10 characters): ");
    scanf("%s", newNode->name);

    printf("Enter phone type (0 for home, 1 for cellular): ");
    scanf("%d", &newNode->type);

    if (newNode->type == 0) {
        printf("Enter area code: ");
        scanf("%d", &newNode->phone.home.area_code);
        printf("Enter number: ");
        scanf("%d", &newNode->phone.home.number);
    } else if (newNode->type == 1) {
        int operatorChoice;
        printf("Enter operator (0 for CHT, 1 for FET, 2 for TWN): ");
        scanf("%d", &operatorChoice);
        newNode->phone.cellular.operator = (Operator)operatorChoice;
        printf("Enter number: ");
        scanf("%d", &newNode->phone.cellular.number);
    } else {
        printf("Invalid phone type. Node not added.\n");
        free(newNode);
        return;
    }

    newNode->next = NULL;

    if (*head == NULL) {
        *head = newNode;
    } else {
        current = *head;
        while (current->next) {
            current = current->next;
        }
        current->next = newNode;
    }

    printf("User added successfully!\n");
}

void printList(Node *head) {
    if (!head) {
        printf("The list is empty.\n");
        return;
    }

    Node *current = head;
    while (current) {
        printf("Name: %s\n", current->name);
        if (current->type == 0) {
            printf("Phone Type: Home\n");
            printf("Area Code: %d\n", current->phone.home.area_code);
            printf("Number: %d\n", current->phone.home.number);
        } else {
            printf("Phone Type: Cellular\n");
            printf("Operator: %s\n", current->phone.cellular.operator == CHT ? "CHT" :
                                             current->phone.cellular.operator == FET ? "FET" : "TWN");
            printf("Number: %d\n", current->phone.cellular.number);
        }
        printf("--------------------\n");
        current = current->next;
    }
}

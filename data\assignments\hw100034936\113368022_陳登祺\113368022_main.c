#include <stdio.h>
#include <stdlib.h>
#include <string.h>

enum  Action {CHT, FET, TWN};

typedef struct queue_node {
    void *content;
    struct queue_node *next;
    struct queue_node *prev;
}tQueueNode; 

typedef struct {
    tQueueNode *front;
    tQueueNode *rear;
    int count;
}tQueue;

typedef struct home_number
{
    char area[4];
    char number[8];
}home_num;

typedef struct cellular_phone
{
    char number[11];
    enum Action operator;
}cell_num;

union phone_num
{
    cell_num cell;
    home_num home;
}number; 

//user info
typedef struct user_info{
    char name[10];
    union phone_num number;
    int type;
}u_info;

//create queue
tQueue* createQueue(void){    
    tQueue *queue;
    queue=(tQueue *) malloc (sizeof(tQueue));

    if (queue)
    {
        queue->front=NULL;
        queue->rear=NULL;  
        queue->count=0;
    }
    return queue;
}

void input_info(tQueue *queue);
void print_queue(tQueue *queue);

int main(void)
{
    tQueue *queue;
    queue = createQueue();
    int choose;
    while (1)
    {
        printf("Enter new user or exit ?(1: enter 2: exit)");
        scanf("%d",&choose);
        if(choose==1)
        {
            printf("Please input your name and phone numbers\n");
            input_info(queue);
            print_queue(queue);
        }
        else
        {
            return 0;
        }
    }
}

void input_info(tQueue *queue){

    tQueueNode *queue_node;
    u_info *info;
    char home_num[10];
    char operator[3];
    int choose;

    queue_node=(tQueueNode *)malloc(sizeof(tQueueNode));
    info=(u_info *)malloc(sizeof(u_info));

    printf("Name(ex: john):");
    scanf("%9s",info->name);
    printf("Home number or Cellphone number ?(home:1 , cell:2)\n");
    scanf("%d",&choose);
    if(choose==1)
    {
        info->type=1;
        printf("Number(ex: 031234567):");
        scanf("%9s",&home_num);
        strncpy(info->number.home.area, home_num, 2); 
        info->number.home.area[2] = '\0'; 
        strncpy(info->number.home.number,home_num+2,7); 
        info->number.home.number[7] = '\0'; 
    }
    else if(choose==2)
    {
        info->type=2;
        printf("Cellphone Number(ex:0906123456):");
        scanf("%10s",info->number.cell.number); 
        printf("Cellphone operator's(ex:CHT):");
        scanf("%s",&operator);
        
        if(strcmp(operator,"CHT")==0)
        {
            info->number.cell.operator=CHT;
        }
        else if(strcmp(operator,"FET")==0)
        {
            info->number.cell.operator=FET;
        }
        else if(strcmp(operator,"TWN")==0)
        {
            info->number.cell.operator=TWN;
        }
    }

    if(queue->count==0)
    {
        queue->count++;
        queue->front=queue_node;
        queue->rear=queue_node;
        queue_node->next==NULL;
        queue_node->prev==NULL;
        queue_node->content=info;
    }
    else
    {
        queue->count++;
        queue_node->content=info;
        queue->rear->next=queue_node;
        queue_node->next=NULL;
        queue_node->prev=queue->rear;
        queue->rear=queue_node;
    }
}

void print_queue(tQueue *queue){
    int i;
    tQueueNode *target = queue->front;
    printf("-------------\n");
    printf("In queue list: \n");  
    printf("-------------\n");  
    for (i = 0; i < queue->count; i++)
    {
        printf("Name: %s\n",((u_info*)target->content)->name);
        if(((u_info*)target->content)->type==1)
        {
            printf("Area code: %s\n",((u_info*)target->content)->number.home.area);
            printf("Number: %s\n",((u_info*)target->content)->number.home.number);
        }
        else
        {
            printf("Operator : %d\n",((u_info*)target->content)->number.cell.operator);
            printf("Number: %s\n",((u_info*)target->content)->number.cell.number);
        }
        printf("-------------\n");
        target = target->next;
    }
    printf("\n");
}

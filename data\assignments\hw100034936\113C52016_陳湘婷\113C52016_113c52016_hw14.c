#include <stdio.h>
#include <stdlib.h>
#include <string.h>

typedef enum {
    CHT = 0,
    FET,
    TWN
} Operator;

typedef struct homeNum {
    char area_code[3];
    char number[8];
} tHome;

typedef struct cellularNum {
    Operator operatorName;
    char phoneNumber[10];
} tCellular;

typedef struct phoneNum {
    int type; // 1 home, 2 cellular
    tHome *homeNum;
    tCellular *cellularNum;
} tPhone;

typedef struct personInfo {
    char name[10];
    tPhone *phoneData;
    struct personInfo *next;
    struct personInfo *prev;
} tDate;

typedef struct {
    tDate *front;
    tDate *rear;
    int count;
} tQueue;

tQueue* createQueue(void);
void print_all_info(tQueue *queue);

int main(void) {
    tQueue *queue;
    queue = createQueue();
    char name[10];
    int type;

    while(1) {
        printf("Input New Info :\n");
        printf("    Input Name: ");
        scanf("%s", name);

        printf("    Which type of phone number? (1: home, 2: cellular): ");
        scanf("%d", &type);

        while (type != 1 && type != 2) {
            printf("Input Error, please try again.\n");
            printf("    Which type of phone number? (1: home, 2: cellular): ");
            scanf("%d", &type);
        }

        tDate *new_data = (tDate *)malloc(sizeof(tDate));
        if (new_data == NULL) {
            printf("Memory allocation failed for new_data.\n");
            exit(1);
        }

        new_data->phoneData = (tPhone *)malloc(sizeof(tPhone));
        if (new_data->phoneData == NULL) {
            printf("Memory allocation failed for phoneData.\n");
            exit(1);
        }

        strcpy(new_data->name, name);
        new_data->phoneData->type = type;
        new_data->next = NULL;
        new_data->prev = NULL;

        if (type == 1) { // Home phone
            new_data->phoneData->homeNum = (tHome *)malloc(sizeof(tHome));
            if (new_data->phoneData->homeNum == NULL) {
                printf("Memory allocation failed for homeNum.\n");
                exit(1);
            }

            printf("        Enter area code (2 digits): ");
            scanf("%s", new_data->phoneData->homeNum->area_code);

            printf("        Enter number (8 digits): ");
            scanf("%s", &new_data->phoneData->homeNum->number);

        } else if (type == 2) { // Cellular phone
            new_data->phoneData->cellularNum = (tCellular *)malloc(sizeof(tCellular));
            if (new_data->phoneData->cellularNum == NULL) {
                printf("Memory allocation failed for cellularNum.\n");
                exit(1);
            }

            printf("        Enter operator (0: CHT, 1: FET, 2: TWN): ");
            int operatorInput;
            scanf("%d", &operatorInput);
            while (operatorInput < 0 || operatorInput > 2) {
                printf("Invalid operator, please try again.\n");
                printf("        Enter operator (0: CHT, 1: FET, 2: TWN): ");
                scanf("%d", &operatorInput);
            }
            new_data->phoneData->cellularNum->operatorName = (Operator)operatorInput;

            printf("        Enter phone number (up to 10 digits): ");
            scanf("%s", new_data->phoneData->cellularNum->phoneNumber);
        }

        if (queue->count == 0) {
            queue->front = new_data;
            queue->rear = new_data;
        } else {
            new_data->prev = queue->rear;
            queue->rear->next = new_data;
            queue->rear = new_data;
        }
        queue->count++;

        print_all_info(queue);

    }

    return 0;
}

tQueue* createQueue(void) {
    tQueue *queue = (tQueue *)malloc(sizeof(tQueue));
    if (queue) {
        queue->front = NULL;
        queue->rear = NULL;
        queue->count = 0;
    }
    return queue;
}

void print_all_info(tQueue *queue) {
    tDate *current = queue->front;
    printf("\n");
    while (current) {
        printf("Name: %s\n", current->name);
        if (current->phoneData->type == 1) { // Home phone
            printf("    Home Phone: Area Code: %s, Number: ", current->phoneData->homeNum->area_code);
            printf("%s", current->phoneData->homeNum->number);
            printf("\n");
        } else if (current->phoneData->type == 2) { // Cellular phone
            const char *operatorNames[] = {"CHT", "FET", "TWN"};
            printf("    Cellular Phone: Operator: %s, Number: %s\n",
                   operatorNames[current->phoneData->cellularNum->operatorName],
                   current->phoneData->cellularNum->phoneNumber);
        }
        current = current->next;
    }
    printf("\n");
}

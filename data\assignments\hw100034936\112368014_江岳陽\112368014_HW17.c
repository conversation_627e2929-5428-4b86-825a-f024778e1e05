#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// 定義電信業者枚舉
typedef enum Operator {
    CHT, // 中華電信
    FET, // 遠傳電信
    TWN  // 台灣大哥大
} Operator;

// 定義電話聯合
typedef union PhoneNumber {
    struct {
        int areaCode;
        char number[10];
    } home;
    struct {
        Operator operator;
        char number[10];
    } cellular;
} PhoneNumber;

// 定義使用者資料結構
typedef struct User {
    char name[10];
    int type; // 0: 家庭電話, 1: 行動電話
    PhoneNumber phone;
    struct User *next;
} User;

// 新增使用者
User *addUser(User *head) {
    User *newUser = (User *)malloc(sizeof(User));
    printf("Enter name: ");
    scanf("%s", newUser->name);
    printf("Enter phone type (0: home, 1: cellular): ");
    scanf("%d", &newUser->type);

    if (newUser->type == 0) {
        printf("Enter area code: ");
        scanf("%d", &newUser->phone.home.areaCode);
        printf("Enter number: ");
        scanf("%s", newUser->phone.home.number);
    } else {
        printf("Enter operator (0: CHT, 1: FET, 2: TWN): ");
        scanf("%d", (int *)&newUser->phone.cellular.operator);
        printf("Enter number: ");
        scanf("%s", newUser->phone.cellular.number);
    }

    newUser->next = head;
    return newUser;
}

// 列印使用者資料
void printUsers(User *head) {
    User *current = head;
    while (current != NULL) {
        printf("Name: %s\n", current->name);
        if (current->type == 0) {
            printf("Phone (Home): (%d) %s\n",
                   current->phone.home.areaCode,
                   current->phone.home.number);
        } else {
            char *operators[] = {"CHT", "FET", "TWN"};
            printf("Phone (Cellular): [%s] %s\n",
                   operators[current->phone.cellular.operator],
                   current->phone.cellular.number);
        }
        current = current->next;
    }
}

// 主程式
int main() {
    User *head = NULL;
    int choice;

    do {
        printf("1. Add User\n2. Show All Users\n0. Exit\n");
        printf("Choose an option: ");
        scanf("%d", &choice);

        switch (choice) {
            case 1:
                head = addUser(head);
                break;
            case 2:
                printUsers(head);
                break;
            case 0:
                printf("Exiting...\n");
                break;
            default:
                printf("Invalid choice!\n");
        }
    } while (choice != 0);

    // 釋放記憶體
    User *temp;
    while (head != NULL) {
        temp = head;
        head = head->next;
        free(temp);
    }

    return 0;
}

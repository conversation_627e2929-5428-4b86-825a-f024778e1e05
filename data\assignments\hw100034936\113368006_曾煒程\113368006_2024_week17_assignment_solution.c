#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define NAME_SIZE 10
#define NUMBER_SIZE 15

typedef enum {
    CHT,
    FET,
    TWN  
} Operator;

typedef struct {
    int area_code;
    char number[NUMBER_SIZE];
} HomeNumber;

typedef struct {
    Operator operator;
    char number[NUMBER_SIZE];
} CellularNumber;

typedef union {
    HomeNumber home;
    CellularNumber cellular;
} PhoneNumber;

typedef struct Contact {
    char name[NAME_SIZE];
    int type; // 0 for home, 1 for cellular
    PhoneNumber phone;
    struct Contact *next;
} Contact;

Contact *head = NULL;

void addContact() {
    Contact *newContact = (Contact *)malloc(sizeof(Contact));

    printf("Enter name (max %d characters): ", NAME_SIZE - 1);
    scanf("%s", newContact->name);

    printf("Enter phone type (0 for home, 1 for cellular): ");
    scanf("%d", &newContact->type);

    if (newContact->type == 0) {
        printf("Enter area code: ");
        scanf("%d", &newContact->phone.home.area_code);

        printf("Enter phone number: ");
        scanf("%s", newContact->phone.home.number);
    } else if (newContact->type == 1) {
        int op;
        printf("Enter operator (0 for CHT, 1 for FET, 2 for TWN): ");
        scanf("%d", &op);
        newContact->phone.cellular.operator = (Operator)op;

        printf("Enter phone number: ");
        scanf("%s", newContact->phone.cellular.number);
    } else {
        printf("Invalid phone type!\n");
        free(newContact);
        return;
    }

    newContact->next = head;
    head = newContact;
}

void displayContacts() {
    Contact *current = head;
    if (!current) {
        printf("No contacts.\n");
        return;
    }
    printf("----------------------\n");
    while (current) {
        printf("Name: %s\n", current->name);
        if (current->type == 0) {
            printf("Phone Type: Home\n");
            printf("Area Code: %d\n", current->phone.home.area_code);
            printf("Number: %s\n", current->phone.home.number);
        } else if (current->type == 1) {
            printf("Phone Type: Cellular\n");
            printf("Operator: ");
            switch (current->phone.cellular.operator) {
                case CHT:
                    printf("CHT\n");
                    break;
                case FET:
                    printf("FET\n");
                    break;
                case TWN:
                    printf("TWN\n");
                    break;
                default:
                    printf("Unknown\n");
                    break;
            }
            printf("Number: %s\n", current->phone.cellular.number);
        }
        current = current->next;
        printf("----------------------\n");
    }
}

void freeContacts() {
    Contact *current = head;
    while (current) {
        Contact *temp = current;
        current = current->next;
        free(temp);
    }
}

int main() {
    int choice;
    do {
        printf("\nPhonebook Menu:\n");
        printf("1. Add Contact\n");
        printf("2. Display Contacts\n");
        printf("3. Exit\n");
        printf("Enter Operation Code: ");
        scanf("%d", &choice);

        switch (choice) {
            case 1:
                addContact();
                break;
            case 2:
                displayContacts();
                break;
            case 3:
                displayContacts();
                freeContacts();
                break;
            default:
                printf("Invalid choice! Please try again.\n");
        }
    } while (choice != 3);

    return 0;
}

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// Define enums for cellular operators
typedef enum {
    CHT, FET, TWN
} Operator;

// Union for phone number types
typedef union {
    struct {
        char areaCode[2];
        int number;
    } home;
    struct {
        Operator operator;
        int number;
    } cellular;
} PhoneNumber;

// Struct for a linked list node
typedef struct Node {
    char name[10];
    char type[10];  // "home" or "cellular"
    PhoneNumber phone;
    struct Node *next;
} Node;

// Function prototypes
void addUser(Node **head);
void printUsers(Node *head);

int main() {
    Node *head = NULL;
    char choice='y';

    while(choice == 'y' || choice == 'Y'){
        addUser(&head);
        printUsers(head);

        printf("\nDo you want to add another user? (y/n): ");
        scanf(" %c", &choice);
    }

    return 0;
}

// Function to add a new user
void addUser(Node **head) {
    Node *newNode = (Node *)malloc(sizeof(Node));
    newNode->next = NULL;

    // Input name
    printf("Enter name (max 10 characters): ");
    scanf("%s", newNode->name);

    // Input phone type
    printf("Enter phone type (home/cellular): ");
    scanf("%s", newNode->type);

    if (strcmp(newNode->type, "home") == 0) {
        // Input home phone details
        printf("Enter area code: ");
        scanf("%s", newNode->phone.home.areaCode);
        printf("Enter number: ");
        scanf("%d", &newNode->phone.home.number);
    } else if (strcmp(newNode->type, "cellular") == 0) {
        // Input cellular phone details
        printf("Enter operator (0: CHT, 1: FET, 2: TWN): ");
        int op;
        scanf("%d", &op);
        newNode->phone.cellular.operator = (Operator)op;

        printf("Enter number: ");
        scanf("%d", &newNode->phone.cellular.number);
    } else {
        printf("Invalid phone type! Skipping entry.\n");
        free(newNode);
        return;
    }

    // Add to linked list
    if (*head == NULL) {
        *head = newNode;
    } else {
        Node *temp = *head;
        while (temp->next != NULL) {
            temp = temp->next;
        }
        temp->next = newNode;
    }
}

// Function to print all users
void printUsers(Node *head) {
    Node *temp = head;
    printf("\nStored Users:\n");
    while (temp != NULL) {
        printf("Name: %s, Type: %s, ", temp->name, temp->type);
        if (strcmp(temp->type, "home") == 0) {
            printf("Area Code: %s, Number: %d\n",
                   temp->phone.home.areaCode, temp->phone.home.number);
        } else if (strcmp(temp->type, "cellular") == 0) {
            printf("Operator: %s, Number: %d\n",
                   temp->phone.cellular.operator == CHT ? "CHT" :
                   temp->phone.cellular.operator == FET ? "FET" : "TWN",
                   temp->phone.cellular.number);
        }
        temp = temp->next;
    }
}

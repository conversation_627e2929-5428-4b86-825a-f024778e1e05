#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// Define operator enum
typedef enum {
    CHT,
    FET,
    TWN
} Operator;

// Define phone number types using union
typedef struct {
    char area_code[4];  
    char number[9];     
} HomePhone;

typedef struct {
    Operator op;
    char number[11]; 
} CellularPhone;

typedef struct {
    int type;        // 0 for home, 1 for cellular
    union {
        HomePhone home;
        CellularPhone cellular;
    } phone;
} PhoneNumber;

// Define node structure for linked list
typedef struct Node {
    char name[10];
    PhoneNumber contact;
    struct Node* next;
} Node;

// Function to create new node
Node* createNode() {
    Node* newNode = (Node*)malloc(sizeof(Node));
    if (newNode == NULL) {
        printf("Memory allocation failed!\n");
        exit(1);
    }
    newNode->next = NULL;
    return newNode;
}

// Function to get operator name
const char* getOperatorName(Operator op) {
    switch(op) {
        case CHT: return "CHT";
        case FET: return "FET";
        case TWN: return "TWN";
        default: return "Unknown";
    }
}

// Function to add new contact
Node* addContact(Node* head) {
    Node* newNode = createNode();
    
    // Get name
    printf("Enter name (max 9 characters): ");
    scanf("%9s", newNode->name);
    getchar(); // Clear buffer
    
    // Get phone type
    printf("Enter phone type (0 for home, 1 for cellular): ");
    scanf("%d", &newNode->contact.type);
    getchar(); // Clear buffer
    
    if (newNode->contact.type == 0) {
        // Home phone
        printf("Enter area code: ");
        scanf("%3s", newNode->contact.phone.home.area_code);  // Read as string to preserve leading zeroes
        getchar(); // Clear buffer
        
        printf("Enter home number: ");
        scanf("%8s", newNode->contact.phone.home.number);
        getchar(); // Clear buffer
    } else {
        // Cellular phone
        printf("Enter operator (0: CHT, 1: FET, 2: TWN): ");
        int op;
        scanf("%d", &op);
        newNode->contact.phone.cellular.op = (Operator)op;
        getchar(); // Clear buffer
        
        printf("Enter cellular number: ");
        scanf("%10s", newNode->contact.phone.cellular.number);
        getchar(); // Clear buffer
    }
    
    // Add to front of list
    newNode->next = head;
    return newNode;
}

// Function to print all contacts
void printContacts(Node* head) {
    printf("\nContact List:\n");
    printf("----------------------------------------\n");
    
    Node* current = head;
    while (current != NULL) {
        printf("Name: %s\n", current->name);
        if (current->contact.type == 0) {
            printf("Phone Type: Home\n");
            printf("Area Code: %s\n", current->contact.phone.home.area_code);  // Print as string to preserve leading zeroes
            printf("Number: %s\n", current->contact.phone.home.number);
        } else {
            printf("Phone Type: Cellular\n");
            printf("Operator: %s\n", getOperatorName(current->contact.phone.cellular.op));
            printf("Number: %s\n", current->contact.phone.cellular.number);
        }
        printf("----------------------------------------\n");
        current = current->next;
    }
}

// Main function
int main() {
    Node* head = NULL;
    char choice;
    
    do {
        printf("\nPhone Book Menu:\n");
        printf("1. Add new contact\n");
        printf("2. Print all contacts\n");
        printf("3. Exit\n");
        printf("Enter your choice: ");
        
        scanf("%c", &choice);
        getchar(); // Clear buffer
        
        switch(choice) {
            case '1':
                head = addContact(head);
                printContacts(head);
                break;
            case '2':
                printContacts(head);
                break;
            case '3':
                printf("Exiting program...\n");
                break;
            default:
                printf("Invalid choice! Please try again.\n");
        }
    } while (choice != '3');
    
    // Free memory
    Node* current = head;
    while (current != NULL) {
        Node* temp = current;
        current = current->next;
        free(temp);
    }
    
    return 0;
}

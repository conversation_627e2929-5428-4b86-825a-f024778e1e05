"""
教學助理Agent主程序
用於自動批改C語言作業
"""

import argparse
import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# 加載環境變數
load_dotenv()

# 添加src目錄到Python路徑
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core import TeachAssistantAgent
from src.utils.logger import logger


def main():
    """主函數"""
    parser = argparse.ArgumentParser(description="教學助理Agent - 自動批改C語言作業")
    
    parser.add_argument(
        "--mode", 
        choices=["single", "batch"], 
        required=True,
        help="處理模式: single(單個文件) 或 batch(批量處理)"
    )
    
    parser.add_argument(
        "--input", 
        required=True,
        help="輸入路徑: 單個壓縮文件路徑(single模式) 或 作業目錄路徑(batch模式)"
    )
    
    parser.add_argument(
        "--student-name",
        help="學生姓名 (僅在single模式下使用)"
    )
    
    parser.add_argument(
        "--requirements",
        help="作業要求文件路徑 (可選，默認使用config/assignment_requirements.txt)"
    )
    
    parser.add_argument(
        "--ai-model",
        choices=["gemini", "openai"],
        help="AI模型選擇 (可選，默認使用配置文件中的設置)"
    )
    
    parser.add_argument(
        "--clear-grades",
        action="store_true",
        help="清空現有成績數據"
    )

    parser.add_argument(
        "--no-plan",
        action="store_true",
        help="不創建執行計劃（默認會創建計劃）"
    )

    parser.add_argument(
        "--no-thinking",
        action="store_true",
        help="不使用思考工具（使用傳統計劃）"
    )

    parser.add_argument(
        "--show-thinking",
        action="store_true",
        help="顯示思考過程和洞察"
    )

    parser.add_argument(
        "--use-template-plan",
        action="store_true",
        help="使用預設模板計劃而不是AI生成計劃"
    )
    
    args = parser.parse_args()
    
    try:
        # 初始化教學助理Agent
        logger.info("初始化教學助理Agent...")
        agent = TeachAssistantAgent(ai_model=args.ai_model)
        
        # 清空成績數據（如果指定）
        if args.clear_grades:
            agent.clear_grades()
            logger.info("已清空現有成績數據")
        
        # 加載作業要求
        requirements = None
        if args.requirements:
            requirements = agent.load_assignment_requirements(args.requirements)
        
        # 根據模式處理作業
        if args.mode == "single":
            logger.info("單個文件處理模式")
            
            if not os.path.exists(args.input):
                logger.error(f"輸入文件不存在: {args.input}")
                return 1
            
            result = agent.process_single_assignment(
                args.input,
                args.student_name,
                requirements,
                create_plan=not args.no_plan,
                use_ai_planning=not args.use_template_plan
            )
            
            if result['success']:
                grade = result['grade_result']['total_score']
                logger.info(f"處理完成！學生: {args.student_name or 'Unknown'}, 分數: {grade}")
            else:
                logger.error(f"處理失敗: {result['error']}")
                return 1
        
        elif args.mode == "batch":
            logger.info("批量處理模式")
            
            if not os.path.exists(args.input):
                logger.error(f"輸入目錄不存在: {args.input}")
                return 1
            
            results = agent.process_batch_assignments(
                args.input,
                requirements,
                create_plan=not args.no_plan,
                use_ai_planning=not args.use_template_plan
            )
            
            # 統計結果
            successful = sum(1 for r in results if r['success'])
            failed = len(results) - successful
            
            logger.info(f"批量處理完成！成功: {successful}, 失敗: {failed}")
            
            if failed > 0:
                logger.warning("以下作業處理失敗:")
                for result in results:
                    if not result['success']:
                        logger.warning(f"  - {result['student_name']}: {result['error']}")
        
        # 導出成績
        grades_file = agent.export_grades()
        logger.info(f"成績已保存到: {grades_file}")

        # 顯示思考洞察（如果啟用）
        if args.show_thinking and not args.no_plan:
            try:
                thinking_insights = agent.get_thinking_insights()
                if 'error' not in thinking_insights:
                    logger.info("🧠 思考過程洞察:")
                    logger.info(f"  思考會話: {thinking_insights.get('session_id', 'N/A')}")
                    logger.info(f"  總思考步驟: {thinking_insights.get('total_thoughts', 0)}")
                    logger.info(f"  思考完成度: {thinking_insights.get('progress_percentage', 0):.1f}%")
                    logger.info(f"  分支數量: {thinking_insights.get('branches_count', 0)}")
                    logger.info(f"  修正次數: {thinking_insights.get('revisions_count', 0)}")

                    # 導出思考日誌
                    thinking_log = agent.export_thinking_log()
                    logger.info(f"  思考日誌: {thinking_log}")
            except Exception as e:
                logger.warning(f"獲取思考洞察失敗: {e}")

        # 顯示總結
        summary = agent.get_grading_summary()
        if 'error' not in summary:
            logger.info(f"📊 評分總結:")
            logger.info(f"  總學生數: {summary['total_students']}")
            logger.info(f"  平均分: {summary['statistics']['mean_score']:.2f}")
            logger.info(f"  編譯成功率: {summary['compilation_stats']['compile_success_rate']:.1f}%")
            logger.info(f"  執行成功率: {summary['compilation_stats']['execute_success_rate']:.1f}%")
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("用戶中斷操作")
        return 1
    except Exception as e:
        logger.error(f"程序執行失敗: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

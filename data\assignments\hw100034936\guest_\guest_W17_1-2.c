#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// Enum for cellular operators
enum Operator {
    CHT,
    FET,
    TWN
};

// Union for different phone types
union PhoneDetail {
    struct {
        char area_code[4];
        char number[9];
    } home;
    struct {
        enum Operator op;
        char number[11];
    } cellular;
};

// Structure for phone information
struct PhoneInfo {
    int type; // 0 for home, 1 for cellular
    union PhoneDetail detail;
};

// Node structure for linked list
struct Node {
    char name[10];
    struct PhoneInfo phone;
    struct Node* next;
};

// Function to create new node
struct Node* createNode() {
    struct Node* newNode = (struct Node*)malloc(sizeof(struct Node));
    newNode->next = NULL;
    return newNode;
}

// Function to print operator name
void printOperator(enum Operator op) {
    switch(op) {
        case CHT: printf("CHT"); break;
        case FET: printf("FET"); break;
        case TWN: printf("TWN"); break;
    }
}

// Function to print all records
void printList(struct Node* head) {
    struct Node* current = head;
    printf("\nAll Records:\n");
    printf("------------------------\n");
    
    while(current != NULL) {
        printf("Name: %s\n", current->name);
        if(current->phone.type == 0) {
            printf("Phone Type: Home\n");
            printf("Area Code: %s\n", current->phone.detail.home.area_code);
            printf("Number: %s\n", current->phone.detail.home.number);
        } else {
            printf("Phone Type: Cellular\n");
            printf("Operator: ");
            printOperator(current->phone.detail.cellular.op);
            printf("\nNumber: %s\n", current->phone.detail.cellular.number);
        }
        printf("------------------------\n");
        current = current->next;
    }
}

int main() {
    struct Node* head = NULL;
    struct Node* current = NULL;
    char choice;
    
    do {
        struct Node* newNode = createNode();
        
        // Get name
        printf("Enter name (max 9 characters): ");
        scanf("%9s", newNode->name);
        getchar(); // Clear buffer
        
        // Get phone type
        printf("Enter phone type (h for home, c for cellular): ");
        char phoneType;
        scanf(" %c", &phoneType);
        
        if(phoneType == 'h') {
            newNode->phone.type = 0;
            printf("Enter area code: ");
            scanf("%s", newNode->phone.detail.home.area_code);
            printf("Enter number: ");
            scanf("%s", newNode->phone.detail.home.number);
        } else {
            newNode->phone.type = 1;
            printf("Enter operator (0:CHT, 1:FET, 2:TWN): ");
            int op;
            scanf("%d", &op);
            newNode->phone.detail.cellular.op = op;
            printf("Enter number: ");
            scanf("%s", newNode->phone.detail.cellular.number);
        }
        
        // Add to linked list
        if(head == NULL) {
            head = newNode;
            current = newNode;
        } else {
            current->next = newNode;
            current = newNode;
        }
        
        // Print all records
        printList(head);
        
        // Ask if want to continue
        printf("\nDo you want to add another record? (y/n): ");
        scanf(" %c", &choice);
        
    } while(choice == 'y' || choice == 'Y');
    
    // Free memory
    current = head;
    while(current != NULL) {
        struct Node* temp = current;
        current = current->next;
        free(temp);
    }
    
    return 0;
}
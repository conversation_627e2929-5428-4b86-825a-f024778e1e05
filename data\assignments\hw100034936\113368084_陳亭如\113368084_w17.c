#include <stdio.h>
#include <stdlib.h>
#include <string.h>

typedef enum {
    CHT,
    FET,
    TWN
} Operator;

typedef union {
    struct {
        int areaCode;
        char number[15];
    } home;
    
    struct {
        Operator op;
        char number[15];
    } cellular;
} PhoneType;

typedef struct Node {
    char name[10];
    int isHomePhone;
    PhoneType phone;
    struct Node* next;
} Node;

const char* getOperatorName(Operator op) {
    switch(op) {
        case CHT: return "CHT";
        case FET: return "FET";
        case TWN: return "TWN";
        default: return "Unknown";
    }
}

Node* createNode() {
    Node* newNode = (Node*)malloc(sizeof(Node));
    if (newNode == NULL) {
        printf("Memory allocation failed!\n");
        exit(1);
    }
    newNode->next = NULL;
    return newNode;
}

Node* addUser(Node* head) {
    Node* newNode = createNode();
    
    printf("Enter name (max 9 characters): ");
    scanf("%9s", newNode->name);
    getchar();
    
    printf("Enter phone type (1 for home, 0 for cellular): ");
    scanf("%d", &newNode->isHomePhone);
    getchar();
    
    if (newNode->isHomePhone) {
        printf("Enter area code: ");
        scanf("%d", &newNode->phone.home.areaCode);
        printf("Enter home phone number: ");
        scanf("%s", newNode->phone.home.number);
    } else {
        printf("Enter operator (0:CHT, 1:FET, 2:TWN): ");
        int op;
        scanf("%d", &op);
        newNode->phone.cellular.op = (Operator)op;
        printf("Enter cellular number: ");
        scanf("%s", newNode->phone.cellular.number);
    }
    
    newNode->next = head;
    return newNode;
}

void printUsers(Node* head) {
    Node* current = head;
    printf("\n=== User Information ===\n");
    while (current != NULL) {
        printf("\nName: %s\n", current->name);
        if (current->isHomePhone) {
            printf("Phone Type: Home\n");
            printf("Area Code: %d\n", current->phone.home.areaCode);
            printf("Number: %s\n", current->phone.home.number);
        } else {
            printf("Phone Type: Cellular\n");
            printf("Operator: %s\n", getOperatorName(current->phone.cellular.op));
            printf("Number: %s\n", current->phone.cellular.number);
        }
        printf("-------------------\n");
        current = current->next;
    }
}

void freeList(Node* head) {
    Node* current = head;
    while (current != NULL) {
        Node* next = current->next;
        free(current);
        current = next;
    }
}

int main() {
    Node* head = NULL;
    char choice;
    
    do {
        head = addUser(head);
        printf("\nAdd another user? (y/n): ");
        scanf(" %c", &choice);
        getchar();
    } while (choice == 'y' || choice == 'Y');
    
    printUsers(head);
    freeList(head);
    
    return 0;
}
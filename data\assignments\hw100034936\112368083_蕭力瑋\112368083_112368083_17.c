#include <stdio.h>
#include <stdlib.h>
#include <string.h>

typedef enum {
    CHT,
    FET,
    TWN
} Operator;

typedef union {
    struct {
        int area_code;
        int number;
    } home;
    struct {
        Operator operator;
        int number;
    } cellular;
} PhoneNumber;

typedef struct Node {
    char name[10];
    int type;
    PhoneNumber phone;
    struct Node *next;
} Node;

Node* createNode(char *name, int type, PhoneNumber phone) {
    Node *newNode = (Node *)malloc(sizeof(Node));
    strcpy(newNode->name, name);
    newNode->type = type;
    newNode->phone = phone;
    newNode->next = NULL;
    return newNode;
}

void appendNode(Node **head, Node *newNode) {
    if (*head == NULL) {
        *head = newNode;
    } else {
        Node *temp = *head;
        while (temp->next != NULL) {
            temp = temp->next;
        }
        temp->next = newNode;
    }
}

void displayList(Node *head) {
    Node *temp = head;
    while (temp != NULL) {
        printf("Name: %s\n", temp->name);
        if (temp->type == 0) {
            printf("Home Phone - Area Code: %d, Number: %d\n", temp->phone.home.area_code, temp->phone.home.number);
        } else {
            printf("Cellular Phone - Operator: %s, Number: %d\n",
                   temp->phone.cellular.operator == CHT ? "CHT" :
                   temp->phone.cellular.operator == FET ? "FET" : "TWN",
                   temp->phone.cellular.number);
        }
        temp = temp->next;
    }
}

int main() {
    Node *head = NULL;
    char name[10];
    int choice, type;
    PhoneNumber phone;

    while (1) {
        printf("Enter 1 to add user, 0 to exit: ");
        scanf("%d", &choice);

        if (choice == 0) break;

        if (choice == 1) {
            printf("Enter Name: ");
            scanf("%s", name);
            printf("Enter 0 for Home or 1 for Cellular: ");
            scanf("%d", &type);

            if (type == 0) {
                printf("Enter Area Code: ");
                scanf("%d", &phone.home.area_code);
                printf("Enter Number: ");
                scanf("%d", &phone.home.number);
            } else {
                printf("Enter Operator (0: CHT, 1: FET, 2: TWN): ");
                scanf("%d", (int *)&phone.cellular.operator);
                printf("Enter Number: ");
                scanf("%d", &phone.cellular.number);
            }

            Node *newNode = createNode(name, type, phone);
            appendNode(&head, newNode);
            displayList(head);
        }
    }

    return 0;
}

#ifndef PHONEBOOK_H
#define PHONEBOOK_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

typedef enum {
    CHT,
    FET,
    TWN
} Operator;

typedef union {
    struct {
        int area_code;
        int number;
    } home;
    struct {
        Operator op; 
        int number;
    } cellular;
} PhoneNumber;

typedef enum {
    HOME,
    CELLULAR
} PhoneType;

typedef struct User {
    char name[10];
    PhoneType type;
    PhoneNumber phone;
    struct User* next;
} User;

User* create_user(const char* name, PhoneType type, PhoneNumber phone);
void add_user(User** head, User* new_user);
void print_users(const User* head);
void free_users(User* head);

#endif 

#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>

enum telecome {CHT, FET, TWN};

typedef struct homeNum {//市話
    int areaCode;
    int num;
} HomeNum;

typedef struct cellNum {//手機
    int telecome;
    int num;
} CellNum;

typedef struct user {
    struct user *next;
    char name[10];
    bool isCellular;
    union {
        HomeNum home;
        CellNum cell;
    } num;
} User;

typedef struct linkedlist {
    User *front;
    int count;
} LinkedList;

LinkedList *createLinkedList(void)
{
    LinkedList *list;
    list = (LinkedList *)malloc(sizeof(LinkedList));

    if (list)
    {
        list->front = NULL;
        list->count = 0;
    }

    return list;
}

void printUser(LinkedList *list)
{
    int i;
    User *target = list->front;
    printf("\n");
    for (i = 0; i < list->count; i++)
    {   
        printf("    User:%s\n", target->name);
        if (target->isCellular) {
            switch (target->num.cell.telecome) {
            case CHT:
                printf("      Operator:CHT" );
                break;
            case FET:
                printf("      Operator:FET" );
                break;
            case TWN:
                printf("      Operator:TWN" );
                break;
            }
            printf(" Number:0%d\n", target->num.cell.num);
        } else {
            printf("      Area code:0%d Number:%d\n", target->num.home.areaCode, target->num.home.num);
        }
        target = target->next;
    }
    printf("\n");
}


int main() {
    LinkedList *list;
    list = createLinkedList();

    while(1) {
        char name[10];
        int ishome;

        User *newUser;
        newUser = (User *)malloc(sizeof(User));
        newUser->next = NULL;
        //輸入       
        printf("Please type new user name: ");
        scanf("%10s", &(newUser->name));
        printf("Choose the number type(0: home, 1: cellular): ");
        scanf("%d", &(newUser->isCellular));

        if (newUser->isCellular) {//手機
            printf("  Operator(0: CHT, 1: FET, 2: TWN): ");
            scanf("%d", &(newUser->num.cell.telecome));
            getchar();
            printf("  Number: ");
            scanf("%d", &(newUser->num.cell.num));
        } else {//市話
            printf("  Area code: ");
            scanf("%d", &(newUser->num.home.areaCode));
            printf("  Number: ");
            scanf("%d", &(newUser->num.home.num));
        }
        //維護linklist
        if(list->count == 0) {
            list->front = newUser;
        } else {
            User *travel = list->front;
            while (travel->next != NULL) {
                travel = travel->next;
            }
            travel->next = newUser;
        }
        list->count ++;
        printUser(list);
    }
}
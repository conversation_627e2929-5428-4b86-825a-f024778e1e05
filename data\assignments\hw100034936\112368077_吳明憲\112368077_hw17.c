#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// Define the phone number types
typedef enum {
    CHT,
    FET,
    TWN
} Operator;

typedef struct {
    char areaCode[4];
    char number[9];
} HomeNumber;

typedef struct {
    Operator operatorName;
    char number[11];
} CellularNumber;

// Define a union for home or cellular phone number
typedef union {
    HomeNumber home;
    CellularNumber cellular;
} PhoneNumber;

// Define the type of phone (home or cellular)
typedef enum {
    HOME,
    CELLULAR
} PhoneType;

// Define the structure for each user
typedef struct User {
    char name[10];
    PhoneType type;
    PhoneNumber phone;
    struct User* next;
} User;

// Function to validate integer input
int getValidatedInt(int min, int max) {
    int value;
    while (1) {
        if (scanf("%d", &value) == 1 && value >= min && value <= max) {
            return value;
        } else {
            printf("Invalid input. Please enter a number between %d and %d: ", min, max);
            while (getchar() != '\n'); // Clear invalid input
        }
    }
}

// Function to validate string input
void getValidatedString(char* str, int maxLength) {
    while (1) {
        scanf("%s", str);
        if (strlen(str) <= maxLength) {
            break;
        } else {
            printf("Invalid input. Please enter a string with at most %d characters: ", maxLength);
        }
    }
}

// Function to print a single user's information
void printUser(User* user) {
    printf("\n--- User Information ---\n");
    printf("Name: %s\n", user->name);
    if (user->type == HOME) {
        printf("Phone Type: Home\n");
        printf("Area Code: %s, Number: %s\n", user->phone.home.areaCode, user->phone.home.number);
    } else {
        printf("Phone Type: Cellular\n");
        printf("Operator: ");
        switch (user->phone.cellular.operatorName) {
            case CHT: printf("CHT\n"); break;
            case FET: printf("FET\n"); break;
            case TWN: printf("TWN\n"); break;
        }
        printf("Number: %s\n", user->phone.cellular.number);
    }
    printf("-------------------------\n");
}

// Function to add a new user to the linked list
void addUser(User** head) {
    User* newUser = (User*)malloc(sizeof(User));
    newUser->next = NULL;

    // Input name
    printf("Enter name (max 10 characters): ");
    getValidatedString(newUser->name, 10);

    // Input phone type
    printf("Enter phone type (0 for home, 1 for cellular): ");
    newUser->type = (PhoneType)getValidatedInt(0, 1);

    // Input phone details based on type
    if (newUser->type == HOME) {
        printf("Enter area code (max 3 characters): ");
        getValidatedString(newUser->phone.home.areaCode, 3);
        printf("Enter number (max 8 characters): ");
        getValidatedString(newUser->phone.home.number, 8);
    } else {
        printf("Enter operator (0 for CHT, 1 for FET, 2 for TWN): ");
        newUser->phone.cellular.operatorName = (Operator)getValidatedInt(0, 2);
        printf("Enter number (max 10 characters): ");
        getValidatedString(newUser->phone.cellular.number, 10);
    }

    // Add to linked list
    if (*head == NULL) {
        *head = newUser;
    } else {
        User* temp = *head;
        while (temp->next != NULL) {
            temp = temp->next;
        }
        temp->next = newUser;
    }

    // Print the newly added user's information
    printUser(newUser);
}

// Function to print all users
void printUsers(User* head) {
    User* temp = head;
    while (temp != NULL) {
        printUser(temp);
        temp = temp->next;
    }
}

// Main function
int main() {
    User* head = NULL;
    int choice;

    do {
        printf("\n1. Add User\n2. Print All Users\n3. Exit\nEnter your choice: ");
        choice = getValidatedInt(1, 3);
        switch (choice) {
            case 1:
                addUser(&head);
                break;
            case 2:
                printUsers(head);
                break;
            case 3:
                printf("Exiting...\n");
                break;
            default:
                printf("Invalid choice. Try again.\n");
        }
    } while (choice != 3);

    // Free memory
    User* temp;
    while (head != NULL) {
        temp = head;
        head = head->next;
        free(temp);
    }

    return 0;
}

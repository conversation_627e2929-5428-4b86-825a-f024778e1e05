"""
文件處理工具
負責解壓縮、文件識別和處理
"""

import os
import shutil
import zipfile
import tarfile
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from ..utils.logger import logger

# 嘗試導入可選的壓縮庫
try:
    import py7zr
    HAS_PY7ZR = True
except ImportError:
    HAS_PY7ZR = False
    logger.warning("py7zr 未安裝，將無法處理 .7z 文件")

try:
    import rarfile
    HAS_RARFILE = True
except ImportError:
    HAS_RARFILE = False
    logger.warning("rarfile 未安裝，將無法處理 .rar 文件")

try:
    import patool
    HAS_PATOOL = True
except ImportError:
    HAS_PATOOL = False
    logger.warning("patool 未安裝，將使用內建的壓縮包處理")


class FileProcessor:
    """文件處理器"""
    
    def __init__(self):
        from ..core.config import config
        file_config = config.get_file_processing_config()

        # 根據可用的庫確定支持的壓縮格式
        self.supported_archives = [".zip", ".tar", ".tar.gz", ".tar.bz2"]
        if HAS_RARFILE:
            self.supported_archives.append(".rar")
        if HAS_PY7ZR:
            self.supported_archives.append(".7z")

        self.supported_code_files = file_config.get("supported_code_files", [".c", ".cpp", ".h"])
        self.extract_path = file_config.get("extract_path", "./data/assignments/extracted")

        # 確保解壓目錄存在
        Path(self.extract_path).mkdir(parents=True, exist_ok=True)

        logger.info(f"支持的壓縮格式: {self.supported_archives}")
    
    def extract_archive(self, archive_path: str, student_name: str = None) -> str:
        """
        解壓縮檔案
        
        Args:
            archive_path: 壓縮檔路徑
            student_name: 學生姓名（可選，用於創建專屬資料夾）
            
        Returns:
            解壓後的資料夾路徑
        """
        archive_path = Path(archive_path)
        
        if not archive_path.exists():
            raise FileNotFoundError(f"壓縮檔不存在: {archive_path}")
        
        # 檢查檔案類型
        file_ext = archive_path.suffix.lower()
        if file_ext not in self.supported_archives:
            raise ValueError(f"不支援的壓縮格式: {file_ext}")
        
        # 創建解壓目標資料夾
        if student_name:
            extract_dir = Path(self.extract_path) / student_name
        else:
            extract_dir = Path(self.extract_path) / archive_path.stem
        
        # 清理舊的解壓資料夾
        if extract_dir.exists():
            shutil.rmtree(extract_dir)
        extract_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"開始解壓縮: {archive_path} -> {extract_dir}")
        
        try:
            # 優先使用 patool 統一處理各種壓縮格式
            if HAS_PATOOL:
                patool.extract_archive(str(archive_path), outdir=str(extract_dir))
                logger.info(f"解壓縮完成: {extract_dir}")
                return str(extract_dir)
            else:
                # 如果沒有 patool，直接使用內建方法
                return self._fallback_extract(archive_path, extract_dir)

        except Exception as e:
            logger.error(f"解壓縮失敗: {e}")
            # 嘗試其他方法
            return self._fallback_extract(archive_path, extract_dir)
    
    def _fallback_extract(self, archive_path: Path, extract_dir: Path) -> str:
        """備用解壓方法"""
        file_ext = archive_path.suffix.lower()

        try:
            if file_ext == '.zip':
                with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                    zip_ref.extractall(extract_dir)
            elif file_ext == '.7z':
                if HAS_PY7ZR:
                    with py7zr.SevenZipFile(archive_path, mode='r') as z:
                        z.extractall(extract_dir)
                else:
                    raise ValueError(f"無法處理 .7z 文件：py7zr 未安裝")
            elif file_ext == '.rar':
                if HAS_RARFILE:
                    with rarfile.RarFile(archive_path) as rf:
                        rf.extractall(extract_dir)
                else:
                    raise ValueError(f"無法處理 .rar 文件：rarfile 未安裝")
            elif file_ext in ['.tar', '.tar.gz', '.tar.bz2']:
                with tarfile.open(archive_path, 'r:*') as tar:
                    tar.extractall(extract_dir)
            else:
                raise ValueError(f"不支援的壓縮格式: {file_ext}")

            logger.info(f"備用方法解壓縮完成: {extract_dir}")
            return str(extract_dir)

        except Exception as e:
            logger.error(f"備用解壓縮方法也失敗: {e}")
            raise
    
    def find_c_files(self, directory: str) -> List[str]:
        """
        在目錄中尋找C語言文件
        
        Args:
            directory: 搜索目錄
            
        Returns:
            C語言文件路徑列表
        """
        c_files = []
        directory = Path(directory)
        
        for file_path in directory.rglob("*"):
            if file_path.is_file() and file_path.suffix.lower() in self.supported_code_files:
                c_files.append(str(file_path))
        
        logger.info(f"在 {directory} 中找到 {len(c_files)} 個C語言文件")
        return c_files
    
    def identify_main_file(self, c_files: List[str]) -> Optional[str]:
        """
        識別主要的C語言文件（包含main函數）
        
        Args:
            c_files: C語言文件列表
            
        Returns:
            主要文件路徑，如果找不到則返回None
        """
        main_files = []
        
        for file_path in c_files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    # 簡單檢查是否包含main函數
                    if 'int main(' in content or 'void main(' in content:
                        main_files.append(file_path)
            except Exception as e:
                logger.warning(f"無法讀取文件 {file_path}: {e}")
        
        if len(main_files) == 1:
            logger.info(f"找到主文件: {main_files[0]}")
            return main_files[0]
        elif len(main_files) > 1:
            logger.warning(f"找到多個主文件: {main_files}")
            # 返回第一個，或者可以根據文件名優先級選擇
            return main_files[0]
        else:
            logger.warning("未找到包含main函數的文件")
            return c_files[0] if c_files else None
    
    def get_student_info(self, directory: str) -> Dict[str, str]:
        """
        從目錄結構中提取學生信息
        
        Args:
            directory: 學生作業目錄
            
        Returns:
            學生信息字典
        """
        directory = Path(directory)
        
        # 嘗試從目錄名稱提取學生信息
        student_name = directory.name
        
        # 查找可能的學生信息文件
        info_files = ['student_info.txt', 'info.txt', 'readme.txt']
        student_info = {'name': student_name, 'id': '', 'directory': str(directory)}
        
        for info_file in info_files:
            info_path = directory / info_file
            if info_path.exists():
                try:
                    with open(info_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        # 這裡可以添加更複雜的解析邏輯
                        student_info['additional_info'] = content
                except Exception as e:
                    logger.warning(f"無法讀取學生信息文件 {info_path}: {e}")
        
        return student_info

#include "phonebook.h"

User* create_user(const char* name, PhoneType type, PhoneNumber phone) {
    User* new_user = (User*)malloc(sizeof(User));
    if (!new_user) {
        printf("Memory allocation failed!\n");
        exit(1);
    }
    strncpy(new_user->name, name, sizeof(new_user->name) - 1);
    new_user->name[sizeof(new_user->name) - 1] = '\0';
    new_user->type = type;
    new_user->phone = phone;
    new_user->next = NULL;
    return new_user;
}

void add_user(User** head, User* new_user) {
    if (!*head) {
        *head = new_user;
    } else {
        User* temp = *head;
        while (temp->next) {
            temp = temp->next;
        }
        temp->next = new_user;
    }
}

void print_users(const User* head) {
    const User* current = head;
    while (current) {
        printf("Name: %s\n", current->name);
        if (current->type == HOME) {
            printf("Phone Type: Home\n");
            printf("Area Code: %d, Number: %d\n", current->phone.home.area_code, current->phone.home.number);
        } else if (current->type == CELLULAR) {
            printf("Phone Type: Cellular\n");
            printf("Operator: %s, Number: %d\n",
                   current->phone.cellular.op == CHT ? "CHT" :
                   current->phone.cellular.op == FET ? "FET" : "TWN",
                   current->phone.cellular.number);
        }
        current = current->next;
        printf("\n");
    }
}

void free_users(User* head) {
    User* temp;
    while (head) {
        temp = head;
        head = head->next;
        free(temp);
    }
}

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

enum Operator {CHT, FET, TWN};

typedef struct member_info{
    char name[10];
    union
    {
        struct {
            char area_number[4];
            char phone_number[11];
        }telephone;

        struct {
            enum Operator operator;
            char phone_number[11];
        }cellphone;
    }phone;
    struct member_info *next;
    struct member_info *prev;
}tQueueNode;

typedef struct {
    tQueueNode *head;
    tQueueNode *tail;
    int count;
}tQueue;

tQueue* createQueue(void);
int enqueue_tele_member(tQueue *queue, char name[10], char area_num[4], char phone_num[11]);
int enqueue_cell_member(tQueue *queue, char name[10], int operation, char phone_num[11]);
void link_list(tQueue *queue, tQueueNode *target_node);
void print_all(tQueue *queue);
void free_queue(tQueue *queue);

int main (void)
{
    int operation, ret;
    char user_name[10];
    char phone_num[11];
    tQueue *queue;
    tQueueNode *target_node;

    queue = createQueue();

    while (1)
    {
        printf("Please input your name: ");
        scanf("%s", user_name);
        while (getchar() != '\n');
        
        printf("  1. Add telephone number.\n");
        printf("  2. Add cellphone number.\n");
        scanf("%d", &operation);
        while (getchar() != '\n');

        if (operation == 1)
        {
            char area_num[4];
            printf("    Input your telephone number (ex. 03-XXXXXXX): ");
            scanf("%3[^-]-%10s", area_num, phone_num);
            while (getchar() != '\n');
            ret = enqueue_tele_member(queue, user_name, area_num, phone_num);
                    
        }else if (operation == 2)
        {
            int user_oper;
            printf("    Choose your telecom operators.\n");
            printf("      0. CHT\n");
            printf("      1. FET\n");
            printf("      2. TWN\n");
            scanf("%d", &user_oper);
            while (getchar() != '\n');

            if (user_oper < 0 || user_oper > 2)
            {
                printf("No such telecom operator!");
            }else
            {
                printf("    Input your cellphone number (ex. 09XXXXXXXX): ");
                scanf("%10s", phone_num);  
                while (getchar() != '\n');
                ret = enqueue_cell_member(queue, user_name, user_oper, phone_num);
            }              
                
        }
        else
        {
            printf("    No such operation!\n");
        }

        if (ret == 0)
        {
            printf("  Enqueue failed!");
        }
        print_all(queue);
    }  
    free_queue(queue);
    return 0; 
}

tQueue* createQueue(void){
    tQueue *queue;
    queue = (tQueue *) malloc (sizeof(tQueue));

    if (queue)
    {
        queue->head = NULL;
        queue->tail = NULL;
        queue->count = 0;
    }
    return queue;
}

int enqueue_tele_member(tQueue *queue, char name[10], char area_num[4], char phone_num[11]){
    tQueueNode *new_node;
    new_node = (tQueueNode *) malloc(sizeof(tQueueNode));
    if(!new_node)
    {
        return 0;
    }

    strcpy(new_node->name, name);
    strcpy(new_node->phone.telephone.area_number, area_num);
    strcpy(new_node->phone.telephone.phone_number, phone_num);
    new_node->next = NULL;
    new_node->prev = NULL;

    link_list(queue, new_node);
    return 1;
}

int enqueue_cell_member(tQueue *queue, char name[10], int operation, char phone_num[11])
{
    tQueueNode *new_node;
    new_node = (tQueueNode *) malloc(sizeof(tQueueNode));
    if (!new_node)
    {
        return 0;
    }
    

    strcpy(new_node->name, name);
    strcpy(new_node->phone.cellphone.phone_number, phone_num);
    new_node->phone.cellphone.operator = operation;
    new_node->next = NULL;
    new_node->prev = NULL;

    link_list(queue, new_node);
    return 1;
}

void link_list(tQueue *queue, tQueueNode *target_node)
{
    if (queue->head == NULL && queue->count == 0)
    {
        queue->head = target_node;
        queue->tail = target_node;
    }else
    {
        target_node->prev = queue->tail;
        queue->tail->next = target_node;
        queue->tail = target_node;
    }

    queue->count++;
}

void print_all(tQueue *queue) {
    tQueueNode *current_node = queue->head;
    
    while (current_node) {
        printf("\n  Name: %s\n", current_node->name);
        if (current_node->phone.cellphone.operator == CHT || current_node->phone.cellphone.operator == FET || current_node->phone.cellphone.operator == TWN) {
            printf("  Type: Cellphone\n");
            printf("  Operator: %s\n", 
                   current_node->phone.cellphone.operator == CHT ? "CHT" :
                   current_node->phone.cellphone.operator == FET ? "FET" : "TWN");
            printf("  Number: %s\n", current_node->phone.cellphone.phone_number);
        } else {
            printf("  Type: Telephone\n");
            printf("  Area number: %s\n", current_node->phone.telephone.area_number);
            printf("  Number: %s\n", current_node->phone.telephone.phone_number);
        }
        current_node = current_node->next;
    }
    printf("\n");
}

void free_queue(tQueue *queue) {
    tQueueNode *current = queue->head;
    while (current) {
        tQueueNode *next = current->next;
        free(current);
        current = next;
    }
    free(queue);
}
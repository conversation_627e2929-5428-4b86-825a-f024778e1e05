#include "main.h"

int main() {
    User* head = NULL;

    while (1) {
        char name[10];
        printf("Enter name (or 'exit' to quit): ");
        scanf("%s", name);

        if (strcmp(name, "exit") == 0) {
            break;
        }

        int phone_type;
        printf("Enter phone type (0 for Home, 1 for Cellular): ");
        scanf("%d", &phone_type);

        PhoneNumber phone;

        if (phone_type == HOME) {
            printf("Enter area code: ");
            scanf("%d", &phone.home.area_code);

            printf("Enter number: ");
            scanf("%d", &phone.home.number);
        } else if (phone_type == CELLULAR) {
            int operator;
            printf("Enter operator (0 for CHT, 1 for FET, 2 for TWN): ");
            scanf("%d", &operator);

            phone.cellular.op = (Operator)operator;

            printf("Enter number: ");
            scanf("%d", &phone.cellular.number);
        } else {
            printf("Invalid phone type. Please try again.\n");
            continue;
        }

        User* new_user = create_user(name, (PhoneType)phone_type, phone);
        add_user(&head, new_user);

        printf("\nAll Users:\n");
        print_users(head);
    }

    free_users(head);
    return 0;
}

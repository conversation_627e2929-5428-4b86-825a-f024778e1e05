#include <stdio.h>
#include <stdlib.h>
#include <string.h>

typedef unsigned char code;
typedef unsigned char uint8;

#define home        1
#define cellular    2

typedef enum opCode{
    CHT = 0,
    FET,
    TWN
}opCode;

typedef struct homePhone{
    char number[10];
    code areaCode;
}homePhoneType;

typedef struct cellularPhone{
    char number[10];
    opCode operatorCode;
}cellularPhoneType;

union DetailInformationType{
    cellularPhoneType cellularPhone;
    homePhoneType homePhone;
};

typedef struct phoneInformation{
    char name[10];
    uint8 type;
    union DetailInformationType DetailInformation;
    struct phoneInformation *next;
}phoneInformation;

phoneInformation* initLinkedList(void);
void addNode(phoneInformation **head, char *name, code info, char *number, uint8 type);
void displayList(phoneInformation *head);
void inputUI(phoneInformation **head);

int main(void)
{
    phoneInformation *head = initLinkedList(); 
    inputUI(&head); 
    return 0;
}


phoneInformation* initLinkedList(void)
{
    return NULL; 
}

void addNode(phoneInformation **head, char *name, code codeInfo, char *number, uint8 type)
{
    phoneInformation *newNode = (phoneInformation *)malloc(sizeof(phoneInformation));
    if (!newNode) {
        printf("Memory allocation failed!\n");
        return;
    }

    strcpy(newNode->name, name);
    newNode->type = type;
    newNode->next = NULL;

    if (type == home) {
        strcpy(newNode->DetailInformation.homePhone.number, number);
        newNode->DetailInformation.homePhone.areaCode = codeInfo;
    } else if (type == cellular) {
        strcpy(newNode->DetailInformation.cellularPhone.number, number);
        newNode->DetailInformation.cellularPhone.operatorCode = (opCode)codeInfo;
    }

    if (*head == NULL) {
        *head = newNode;
    } else {
        phoneInformation *current = *head;
        while (current->next != NULL) {
            current = current->next;
        }
        current->next = newNode;
    }
}

void displayList(phoneInformation *head)
{
    phoneInformation *current = head;
    int count = 1;
    const char *operatorNames[] = {"CHT", "FET", "TWN"}; 

    printf("The list content:\n");
    while (current != NULL) {
        printf("Data order (%d):\n", count);
        printf("Name: %s\n", current->name);

        if (current->type == home) {
            printf("Phone type: home\n");
            printf("Area code: %d\n", current->DetailInformation.homePhone.areaCode);
            printf("Number: %s\n", current->DetailInformation.homePhone.number);
        } else if (current->type == cellular) {
            printf("Phone type: cellular\n");
            printf("Operator code: %s\n", operatorNames[current->DetailInformation.cellularPhone.operatorCode]);
            printf("Number: %s\n", current->DetailInformation.cellularPhone.number);
        }

        printf("\n");
        current = current->next;
        count++;
    }
    printf("\n");
}


void inputUI(phoneInformation **head)
{
    char name[10], number[10];
    int type, codeInfo;

    while (1) {
        printf("Please enter the name: ");
        scanf("%s", name);
        printf("Please enter the user type (1: home phone, 2: cellular phone): ");
        scanf("%d", &type);

        if (type != home && type != cellular) {
            printf("\nError: Invalid type!\n\n");
            continue;
        }

        if (type == home) {
            printf("Please enter area code: ");
            scanf("%d", &codeInfo);
        } else {
            printf("Please enter operator code (0: CHT, 1: FET, 2: TWN): ");
            scanf("%d", &codeInfo);
            if (codeInfo > TWN) {
                printf("\nError: Invalid operator code!\n\n");
                continue;
            }
        }

        printf("Please enter the phone number: ");
        scanf("%s", number);

        addNode(head, name, codeInfo, number, type);
        displayList(*head);
    }
}

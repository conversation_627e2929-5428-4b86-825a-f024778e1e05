//
//  main.c
//  week17_c
//
//  Created by 莊善傑 on 2025/1/2.
//

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// Enumeration for cellular operator's name
typedef enum { CHT, FET, TWN } OperatorName;

// Union to integrate home and cellular phone number types
typedef union {
    struct {
        char areaCode[6]; // Max 5 characters + null terminator
        char number[16];  // Max 15 characters + null terminator
    } homePhone;
    struct {
        OperatorName operator;
        char number[16];  // Max 15 characters + null terminator
    } cellPhone;
} PhoneNumber;

// Structure for a contact with a name and a phone number
typedef struct Contact {
    char name[10];        // Max 9 characters + null terminator
    PhoneNumber phone;    // Union for home/cellular
    int isCellular;       // 0 for home phone, 1 for cellular phone
    struct Contact* next; // Pointer to the next contact
} Contact;

// Function to create a new contact
Contact* createContact(const char* name, PhoneNumber phone, int isCellular) {
    Contact* newContact = (Contact*)malloc(sizeof(Contact));
    if (newContact) {
        strncpy(newContact->name, name, 9);
        newContact->name[9] = '\0';
        newContact->phone = phone;
        newContact->isCellular = isCellular;
        newContact->next = NULL;
    }
    return newContact;
}

// Function to add a contact to the linked list
void addContact(Contact** head, Contact* newContact) {
    if (*head == NULL) {
        *head = newContact;
    } else {
        Contact* current = *head;
        while (current->next != NULL) {
            current = current->next;
        }
        current->next = newContact;
    }
}

// Function to print all contacts
void printContacts(Contact* head) {
    Contact* current = head;
    while (current != NULL) {
        printf("Name: %s\n", current->name);
        if (current->isCellular) {
            const char* operatorNames[] = { "CHT", "FET", "TWN" };
            printf("Cellular - Operator: %s, Number: %s\n",
                   operatorNames[current->phone.cellPhone.operator],
                   current->phone.cellPhone.number);
        } else {
            printf("Home - Area Code: %s, Number: %s\n",
                   current->phone.homePhone.areaCode,
                   current->phone.homePhone.number);
        }
        current = current->next;
    }
}

// Function to free all contacts
void freeContacts(Contact** head) {
    Contact* current = *head;
    Contact* next;
    while (current != NULL) {
        next = current->next;
        free(current);
        current = next;
    }
    *head = NULL;
}

int main() {
    Contact* head = NULL;
    char name[10];
    PhoneNumber phone;
    int type;
    int operatorInput;

    while (1) {
        printf("Enter Name (or 'exit' to finish): ");
        scanf("%9s", name);
        if (strcmp(name, "exit") == 0) {
            break;
        }

        printf("Enter 0 for home phone or 1 for cellular phone: ");
        scanf("%d", &type);

        if (type == 0) {
            printf("Enter Area Code: ");
            scanf("%5s", phone.homePhone.areaCode);
            printf("Enter Number: ");
            scanf("%15s", phone.homePhone.number);
        } else {
            printf("Enter Operator (0 for CHT, 1 for FET, 2 for TWN): ");
            scanf("%d", &operatorInput);
            phone.cellPhone.operator = (OperatorName)operatorInput;
            printf("Enter Number: ");
            scanf("%15s", phone.cellPhone.number);
        }

        addContact(&head, createContact(name, phone, type));
    }

    printf("\nAll contacts:\n");
    printContacts(head);
    freeContacts(&head);

    return 0;
}

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define NAME_LENGTH 10
#define AREA_CODE_LENGTH 3
#define HOME_NUMBER_LENGTH 9
#define CELLULAR_NUMBER_LENGTH 11

typedef enum
{
    HOME = 1,
    CELLULAR
} PhoneType;

typedef enum
{
    CHT = 1,
    FET,
    TWN
} Operator;

typedef union
{
    struct
    {
        char area_code[AREA_CODE_LENGTH];
        char number[HOME_NUMBER_LENGTH];
    } home;
    struct
    {
        Operator operator_name;
        char number[CELLULAR_NUMBER_LENGTH];
    } cellular;
} PhoneNumber;

typedef struct User
{
    char name[NAME_LENGTH];
    PhoneType phone_type;
    PhoneNumber phone_number;
    struct User *next;
} User;

void add_user(User **head);
void display_users(const User *head);
const char *get_phone_type(PhoneType type);
const char *get_operator_name(Operator op);

int main()
{
    User *head = NULL;

    while (1)
    {
        add_user(&head);
        display_users(head);
    }

    return 0;
}

void add_user(User **head)
{
    User *new_user = (User *)malloc(sizeof(User));
    if (new_user == NULL)
    {
        printf("Memory allocation failed.\n");
        return;
    }

    printf("Enter name (max %d characters): ", NAME_LENGTH - 1);
    fgets(new_user->name, sizeof(new_user->name), stdin);  // Dùng fgets để đọc tên

    // Nếu người dùng nhập -1, chương trình sẽ dừng
    if (strcmp(new_user->name, "-1\n") == 0)
    {
        printf("Exiting the program.\n");
        exit(0);  // Thoát chương trình
    }

    // Loại bỏ ký tự newline '\n' sau khi nhập tên
    new_user->name[strcspn(new_user->name, "\n")] = '\0';

    printf("Enter phone type (1. HOME, 2. CELLULAR): ");
    scanf("%u", (unsigned int *)&new_user->phone_type);
    getchar();  // Xử lý ký tự newline còn sót lại sau khi nhập phone type

    if (new_user->phone_type == HOME)
    {
        printf("Enter area code : ");
        scanf("%2s", new_user->phone_number.home.area_code);
        printf("Enter number : ");
        scanf("%8s", new_user->phone_number.home.number);
    }
    else if (new_user->phone_type == CELLULAR)
    {
        printf("Enter operator (1. CHT, 2. FET, 3. TWN): ");
        int op;
        scanf("%u", (unsigned int *)&new_user->phone_number.cellular.operator_name);
        printf("Enter number : ");
        scanf("%10s", new_user->phone_number.cellular.number);
    }
    else
    {
        printf("Invalid phone type. User not added.\n");
        free(new_user);
        return;
    }

    new_user->next = *head;
    *head = new_user;
}

void display_users(const User *head)
{
    if (head == NULL)
    {
        printf("No users to display.\n");
        return;
    }

    printf("\nUser List:\n");
    printf("%-10s %-10s %-15s %-15s\n", "Name", "Type", "Operator/Area", "Number");
    printf("-----------------------------------------------------------\n");

    const User *current = head;
    while (current != NULL)
    {
        if (current->phone_type == HOME)
        {
            printf("%-10s %-10s %-15s %-15s\n",
                   current->name,
                   get_phone_type(current->phone_type),
                   current->phone_number.home.area_code,
                   current->phone_number.home.number);
        }
        else if (current->phone_type == CELLULAR)
        {
            printf("%-10s %-10s %-15s %-15s\n",
                   current->name,
                   get_phone_type(current->phone_type),
                   get_operator_name(current->phone_number.cellular.operator_name),
                   current->phone_number.cellular.number);
        }
        current = current->next;
    }
}

const char *get_phone_type(PhoneType type)
{
    const char *phone_type[] = {"", "HOME", "CELLULAR"};
    return phone_type[type];
}

const char *get_operator_name(Operator op)
{
    const char *operator_names[] = {"", "CHT", "FET", "TWN"};
    return operator_names[op];
}

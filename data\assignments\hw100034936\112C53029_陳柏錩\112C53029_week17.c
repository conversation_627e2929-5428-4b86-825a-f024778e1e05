#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// Enum for cellular operator names
typedef enum {
    CHT, FET, TWN
} Operator;

// Union for phone number types
typedef union {
    struct {
        int areaCode;
        char number[10];
    } home;
    struct {
        Operator operatorName;
        char number[10];
    } cellular;
} PhoneNumber;

// Struct for user information
typedef struct User {
    char name[10];
    int phoneType; // 0 for home, 1 for cellular
    PhoneNumber phoneNumber;
    struct User *next;
} User;

// Function to create a new user node
User* createUser(char name[], int phoneType, PhoneNumber phoneNumber) {
    User *newUser = (User *)malloc(sizeof(User));
    strcpy(newUser->name, name);
    newUser->phoneType = phoneType;
    newUser->phoneNumber = phoneNumber;
    newUser->next = NULL;
    return newUser;
}

// Function to print all user information
void printUsers(User *head) {
    User *current = head;
    while (current != NULL) {
        printf("Name: %s\n", current->name);
        if (current->phoneType == 0) {
            printf("Phone Type: Home\n");
            printf("Area Code: %d, Number: %s\n", current->phoneNumber.home.areaCode, current->phoneNumber.home.number);
        } else {
            printf("Phone Type: Cellular\n");
            printf("Operator: %s\n", current->phoneNumber.cellular.operatorName == CHT ? "CHT" :
                                      current->phoneNumber.cellular.operatorName == FET ? "FET" : "TWN");
            printf("Number: %s\n", current->phoneNumber.cellular.number);
        }
        current = current->next;
    }
}

int main() {
    User *head = NULL, *tail = NULL;
    char choice;

    do {
        char name[10];
        int phoneType;
        PhoneNumber phoneNumber;

        printf("Enter Name (max 10 characters): ");
        scanf("%s", name);

        printf("Enter Phone Type (0 for Home, 1 for Cellular): ");
        scanf("%d", &phoneType);

        if (phoneType == 0) {
            printf("Enter Area Code: ");
            scanf("%d", &phoneNumber.home.areaCode);
            printf("Enter Number: ");
            scanf("%s", phoneNumber.home.number);
        } else if (phoneType == 1) {
            int operatorChoice;
            printf("Enter Operator (0 for CHT, 1 for FET, 2 for TWN): ");
            scanf("%d", &operatorChoice);
            phoneNumber.cellular.operatorName = (Operator)operatorChoice;
            printf("Enter Number: ");
            scanf("%s", phoneNumber.cellular.number);
        } else {
            printf("Invalid Phone Type! Try again.\n");
            continue;
        }

        User *newUser = createUser(name, phoneType, phoneNumber);
        if (head == NULL) {
            head = tail = newUser;
        } else {
            tail->next = newUser;
            tail = newUser;
        }

        printf("Do you want to add another user? (y/n): ");
        scanf(" %c", &choice);

    } while (choice == 'y' || choice == 'Y');

    printf("\nUser Information:\n");
    printUsers(head);

    // Free allocated memory
    User *current = head;
    while (current != NULL) {
        User *temp = current;
        current = current->next;
        free(temp);
    }

    return 0;
}

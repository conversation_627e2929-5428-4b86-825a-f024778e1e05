#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// Enum for mobile operators
typedef enum {
    CHT,
    FET,
    TWN
} Operator;

// Union for phone number types
typedef union {
    struct {
        int areaCode;
        int number;
    } home;
    struct {
        Operator operator;
        int number;
    } cellular;
} PhoneNumber;

// Struct for user information
typedef struct User {
    char name[10];
    int phoneType; // 0 for home, 1 for cellular
    PhoneNumber phone;
    struct User *next;
} User;

// Function to create a new user node
User *createUser() {
    User *newUser = (User *)malloc(sizeof(User));
    if (!newUser) {
        printf("Memory allocation failed!\n");
        exit(1);
    }

    printf("Enter name: ");
    scanf("%9s", newUser->name);

    printf("Enter phone type (0 for home, 1 for cellular): ");
    scanf("%d", &newUser->phoneType);

    if (newUser->phoneType == 0) {
        printf("Enter area code: ");
        scanf("%d", &newUser->phone.home.areaCode);
        printf("Enter number: ");
        scanf("%d", &newUser->phone.home.number);
    } else if (newUser->phoneType == 1) {
        printf("Enter operator (0 for CHT, 1 for FET, 2 for TWN): ");
        scanf("%d", (int *)&newUser->phone.cellular.operator);
        printf("Enter number: ");
        scanf("%d", &newUser->phone.cellular.number);
    } else {
        printf("Invalid phone type!\n");
        free(newUser);
        return NULL;
    }

    newUser->next = NULL;
    return newUser;
}

// Function to print all users
void printUsers(User *head) {
    User *current = head;
    while (current) {
        printf("Name: %s\n", current->name);
        if (current->phoneType == 0) {
            printf("Phone Type: Home\n");
            printf("Area Code: %d, Number: %d\n", current->phone.home.areaCode, current->phone.home.number);
        } else {
            printf("Phone Type: Cellular\n");
            printf("Operator: %s, Number: %d\n",
                   current->phone.cellular.operator == CHT ? "CHT" :
                   current->phone.cellular.operator == FET ? "FET" : "TWN",
                   current->phone.cellular.number);
        }
        current = current->next;
        printf("\n");
    }
}

int main() {
    User *head = NULL, *tail = NULL;
    char choice;

    do {
        User *newUser = createUser();
        if (newUser) {
            if (!head) {
                head = tail = newUser;
            } else {
                tail->next = newUser;
                tail = newUser;
            }
        }

        printf("\nDo you want to add another user? (y/n): ");
        scanf(" %c", &choice);
    } while (choice == 'y' || choice == 'Y');

    printf("\nAll Users:\n");
    printUsers(head);

    // Free the linked list
    User *current = head;
    while (current) {
        User *temp = current;
        current = current->next;
        free(temp);
    }

    return 0;
}
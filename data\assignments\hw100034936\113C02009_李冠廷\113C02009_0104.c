#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define HOME 0
#define CELL 1

typedef enum operator{
    CHT, // Chunghwa Telecom
    FET, // FarEasTone
    TWN  // Taiwan Mobile
} Operator;

typedef struct user
{
    struct user *next;
    char name[10];

    union info
    {
        struct
        {
            int area_code;
            int num;
        } home;
        struct
        {
            Operator operator;
            int num;
        } cell;
    } info;
    int type;
} tuser;

typedef struct user_head
{
    int count;
    struct user *head;
    struct user *tail;
} tHead;

void get_information(tuser *list)
{
    int op, operator;
    printf("Please enter the name: ");
    scanf("%s", list->name);
    printf("Please select what type (0)home (1)cellular you want to record: ");
    scanf("%d", &op);
    list->type = op;

    if (op == HOME)
    {
        printf("Please enter the home area code: ");
        scanf("%d", &list->info.home.area_code);
        printf("Please enter the home number: ");
        scanf("%d", &list->info.home.num);
    }
    else if (op == CELL)
    {
        printf("Please select the operator (0)CHT (1)FET (2)TWN: ");
        scanf("%d", &operator);
        list->info.cell.operator = (Operator)operator;
        printf("Please enter the cellular number: ");
        scanf("%d", &list->info.cell.num);
    }
    else
    {
        return;
    }
}

void initial_list(tHead *list)
{
    list->count = 0;
    list->head = NULL;
    list->tail = NULL;
}

void add_user(tHead *list, tuser *new_user)
{
    new_user->next = NULL;
    if (list->head == NULL)
    {
        list->head = new_user;
        list->tail = new_user;
    }
    else
    {
        list->tail->next = new_user;
        list->tail = new_user;
    }
    list->count++;
}

void print_list(tHead *list)
{
    tuser *current = list->head;
    while (current != NULL)
    {
        printf("Name: %s\n", current->name);
        if (current->type == HOME)
        {
            printf("Home area code: %d\n", current->info.home.area_code);
            printf("Home number: %d\n", current->info.home.num);
        }
        else if (current->type == CELL)
        {
            printf("Cellular operator: ");
            switch (current->info.cell.operator)
            {
                case CHT:
                    printf("CHT\n");
                    break;
                case FET:
                    printf("FET\n");
                    break;
                case TWN:
                    printf("TWN\n");
                    break;
            }
            printf("Cellular number: %d\n", current->info.cell.num);
        }
        current = current->next;
    }
}

int main(void)
{
    tHead list;
    initial_list(&list);
    while (1)
    {
        tuser *new_user = (tuser *)malloc(sizeof(tuser));
        get_information(new_user);
        add_user(&list, new_user);
        print_list(&list);
    }

    return 0;
}
